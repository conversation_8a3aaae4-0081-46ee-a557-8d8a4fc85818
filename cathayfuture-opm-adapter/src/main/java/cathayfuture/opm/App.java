package cathayfuture.opm;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@MapperScan({
        "cathayfuture.opm.infra.**.repository.mapper"
})
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan({"cathayfuture.opm", "com.dtyunxi.cube", "com.dtyunxi.yundt.cube",
        "com.dtyunxi.huieryun.oss",
        "com.dtyunxi.huieryun.cache",
        "com.dtyunxi.huieryun.lock.api",
        "com.dtyunxi.tasly.center.user.api"})
@EnableFeignClients(basePackages = {"com.dtyunxi.yundt.cube",
        "com.dtyunxi.tasly.center.user.api"})
public class App {

    public static void main(String[] args) {
        SpringApplication.run(App.class, args);
    }
}
