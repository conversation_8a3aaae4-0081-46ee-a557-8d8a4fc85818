package cathayfuture.opm.adapter.bms.order.controller;

import cathayfuture.opm.app.common.ExcelUploadService;
import cathayfuture.opm.client.order.dto.request.ExcelUploadReqDTO;
import com.dtyunxi.rest.RestResponse;
import com.taslyware.framework.exceltools.importer.vo.ImportResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/30/22
 */
@RestController
@RequestMapping("/v1/excel/upload")
public class ExcelUploadController {

    @Resource
    private ExcelUploadService excelUploadService;

    @PostMapping("/{type}")
    @ApiOperation(value = "上传", notes = "上传")
    RestResponse uploadByType(HttpServletResponse response, @PathVariable("type") String code, @RequestBody ExcelUploadReqDTO excelUploadReqDto) {
        ImportResponse importResponse = excelUploadService.uploadByCode(response, code, excelUploadReqDto);
        switch (importResponse.getResultEnum()) {
            case DOWNLOAD:
                return null;
            case RESPONSEBODY:
                return new RestResponse("0", importResponse.getMessage(), importResponse.getData());
            case ERROR:
                return new RestResponse("400003", importResponse.getMessage(), importResponse.getData());
            default:
                return RestResponse.SUCCEED;
        }
    }
}