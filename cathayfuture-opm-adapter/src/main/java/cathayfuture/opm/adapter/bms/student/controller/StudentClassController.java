package cathayfuture.opm.adapter.bms.student.controller;

import cathayfuture.opm.adapter.CommonResponseDTO;
import cathayfuture.opm.client.order.api.ComputationalCostsAppService;
import cathayfuture.opm.client.student.api.StudentClassAppService;
import cathayfuture.opm.client.student.api.query.StudentClassQueryAppService;
import io.swagger.models.auth.In;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 班级
 * @date 2023/4/4 14:42
 */
@Slf4j
@RestController(value = "StudentClassController")
@Tag(name = "StudentClassController", description = "班级api")
@RequestMapping("/bms/studentClass")
public class StudentClassController {

    private StudentClassQueryAppService studentClassQueryAppService;

    private StudentClassAppService studentClassAppService;
    @Resource
    private ComputationalCostsAppService computationalCostsAppService;

    public StudentClassController(StudentClassQueryAppService studentClassQueryAppService, StudentClassAppService studentClassAppService) {
        this.studentClassQueryAppService = studentClassQueryAppService;
        this.studentClassAppService = studentClassAppService;
    }

    @GetMapping("/queryAllClassNames")
    @Operation(summary = "查询所有班级名称", description = "查询所有班级名称")
    public CommonResponseDTO<List<String>> queryAllClassNames(){
        return CommonResponseDTO.success(studentClassQueryAppService.findAllNames());
    }

    @GetMapping("/childCareOperate")
    @Operation(summary = "测试生成保育费", description = "测试生成保育费")
    public CommonResponseDTO testChildCareOperate(@RequestParam(value = "studentIds",required = false) String studentIds,
                                                  @RequestParam(value = "dateStr",required = false) String dateStr){
        List<Integer> list = new ArrayList<>();
        if(StringUtils.isNotBlank(studentIds)){
            list = Arrays.stream(studentIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        computationalCostsAppService.childCareOperate(list,dateStr);
        return CommonResponseDTO.success();
    }

    @GetMapping("/dinnerOperate")
    @Operation(summary = "测试生成餐费", description = "测试生成餐费")
    public CommonResponseDTO testDinnerOperate(@RequestParam(value = "studentIds",required = false) String studentIds,
                                               @RequestParam(value = "dateStr",required = false) String dateStr){
        List<Integer> list = new ArrayList<>();
        if(StringUtils.isNotBlank(studentIds)){
            list = Arrays.stream(studentIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        computationalCostsAppService.dinnerOperate(list,dateStr);
        return CommonResponseDTO.success();
    }

    @GetMapping("/monthlySettlementOperate")
    @Operation(summary = "测试保育费月结", description = "测试保育费月结")
    public CommonResponseDTO monthlySettlementOperate(@RequestParam(value = "studentIds",required = false) String studentIds,
                                               @RequestParam(value = "dateStr",required = false) String dateStr){
        List<Integer> list = new ArrayList<>();
        if(StringUtils.isNotBlank(studentIds)){
            list = Arrays.stream(studentIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
        computationalCostsAppService.monthlySettlementOperate(list,dateStr);
        return CommonResponseDTO.success();
    }

    @GetMapping("/monthlySettlementForDinnerOperate")
    @Operation(summary = "测试餐费月结", description = "测试餐费月结")
    public CommonResponseDTO monthlySettlementForDinnerOperate(@RequestParam(value = "studentIds",required = false) String studentIds,
                                                      @RequestParam(value = "dateStr",required = false) String dateStr){
        List<Integer> list = new ArrayList<>();
        if(StringUtils.isNotBlank(studentIds)){
            list = Arrays.stream(studentIds.split(","))
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }


        computationalCostsAppService.monthlySettlementForDinnerOperate(list,dateStr);
        return CommonResponseDTO.success();
    }


}
