/**
 * @(#) DiscoveryConfig.java 1.1.0 2019-03-13
 * Copyright (c)  2019, YUNXI. All rights reserved.
 * YUNXI PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package cathayfuture.opm.adapter.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Configuration;

/**
 * 运行环境变量说明: edas: 支持hsf 和 restful, 部署环境: 开发用阿里轻量级配置中心,生产EDAS + ACM
 * <p>
 * edas_springcloud: 只支持restful,部署环境:开发用用阿里轻量级配置中心,生产EDAS + ACM
 * <p>
 * springcloud: 只支持restful, 开源的springcloud或ali-springcloud
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ConditionalOnExpression("'${dtyunxi.deploy}' == 'edas_springcloud' or '${dtyunxi.deploy}' == 'springcloud'")
@Configuration
@EnableDiscoveryClient
public class DiscoveryConfig {

}
