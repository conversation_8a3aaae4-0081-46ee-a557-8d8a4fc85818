package cathayfuture.opm.adapter.config;

import cathayfuture.opm.adapter.config.interceptor.ExtServiceContextInterceptor;
import cathayfuture.opm.adapter.config.interceptor.WxAuthenticationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class MvcConfiguration implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
                .allowCredentials(true)
                .maxAge(3600)
                .allowedHeaders("*");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(wxAuthenticationInterceptor())
                .addPathPatterns("/mobile/**")
                .excludePathPatterns("/mobile/wx/login", "/mobile/callbacks/**");
        registry.addInterceptor(serviceContextInterceptor()).order(Ordered.LOWEST_PRECEDENCE);
    }

    @Bean
    public WxAuthenticationInterceptor wxAuthenticationInterceptor() {
        return new WxAuthenticationInterceptor();
    }

    @Bean
    public ExtServiceContextInterceptor serviceContextInterceptor() {
        return new ExtServiceContextInterceptor();
    }
}

