package cathayfuture.opm.adapter.config;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.servers.Server;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.context.annotation.Configuration;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 8/24/22
 */
//@OpenAPIDefinition(
//        tags = {
//                @Tag(name = "用户管理", description = "用户模块操作"),
//                @Tag(name = "订单管理", description = "订单模块操作")
//        },
//        info = @Info(
//                title = "用户接口 API 文档",
//                description = "用户数据管理......",
//                version = "1.0.0",
//                contact = @Contact(name = "XX", email = "<EMAIL>", url = "https://www.XX.com"),
//                license = @License(name = "Apache 2.0", url = "http://www.apache.org/licenses/LICENSE-2.0.html")
//        ),
//        servers = {
//                @Server(description = "开发环境服务器", url = "https://xxxx.com/api/v1"),
//                @Server(description = "测试环境服务器", url = "https://test.xxxx.com/api/v1")
//        },
//        security = @SecurityRequirement(name = "Oauth2"),
//        externalDocs = @ExternalDocumentation(
//                description = "项目编译部署说明",
//                url = "http://localhost/deploy/README.md"
//        )
//)
@Configuration
public class OpenApiConfiguration {
}