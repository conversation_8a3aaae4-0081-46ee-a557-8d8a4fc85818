package cathayfuture.opm.adapter.config.interceptor;

import cathayfuture.opm.infra.common.ExtServiceContext;
import com.alibaba.fastjson.JSON;
import com.dtyunxi.app.ServiceContext;
import com.dtyunxi.huieryun.cache.api.ICacheService;
import com.dtyunxi.rest.RestResponse;
import com.dtyunxi.tasly.center.user.api.dto.response.PostExtRespDto;
import com.dtyunxi.tasly.center.user.api.query.IPostQueryExtApi;
import com.dtyunxi.yundt.cube.center.user.api.dto.EmployeeDto;
import com.dtyunxi.yundt.cube.center.user.api.dto.RoleDto;
import com.dtyunxi.yundt.cube.center.user.api.dto.UserDto;
import com.dtyunxi.yundt.cube.center.user.api.dto.vo.UserAccessVo;
import com.dtyunxi.yundt.cube.center.user.api.query.IAccessQueryApi;
import com.dtyunxi.yundt.cube.center.user.api.query.IOrganizationQueryApi;
import com.dtyunxi.yundt.cube.center.user.api.query.IUserQueryApi;
import com.taslyware.framework.exceltools.utils.ContextCommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/13/22
 */
@Slf4j
public class ExtServiceContextInterceptor implements HandlerInterceptor {


    private ThreadLocal<Integer> userInfo = new ThreadLocal<>();

    @Resource
    private IAccessQueryApi accessQueryApi;

    @Resource
    private IOrganizationQueryApi organizationQueryApi;

    @Resource
    private IPostQueryExtApi postQueryExtApi;

    @Resource
    private IUserQueryApi userQueryApi;

    @Resource
    private ICacheService redisCache;

    private static final int CACHE_TIME_OUT = 3600;
    private static final String CACHE_PREFIX = "CATHAYFUTURE_OPM_";

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) {
        Long userId = ServiceContext.getContext().getRequestUserId();
        String userIdStr = ContextCommonUtils.getRequestHeaders(request).get(ExtServiceContext.USER_ID);
        log.info("用户角色穿透: userId is [{}], userIdStr is [{}]", userId, userIdStr);
        if (userInfo.get() != null || userId == null) {
            return true;
        }

        Long instanceId = 1L;
        userInfo.set(instanceId.intValue());
        log.info("用户角色穿透：instanceId为" + instanceId);
        try {
            if (getCache(ExtServiceContext.ROLE_ID, userId) != null) {
                log.info("role id is not null, role id is [{}]", getCache(ExtServiceContext.ROLE_ID, userId));
                setHeader(ExtServiceContext.ROLE_ID, getCache(ExtServiceContext.ROLE_ID, userId));
            } else {
                RestResponse<UserAccessVo> userAccessVoRestResponse = accessQueryApi.queryUserAccess(instanceId, userId, "{}");
                if (userAccessVoRestResponse.getData() != null) {
                    Set<RoleDto> roleSet = userAccessVoRestResponse.getData().getRoleSet();
                    if (!roleSet.isEmpty()) {
                        String roleIds = roleSet.stream().map(t -> String.valueOf(t.getId())).collect(Collectors.joining(","));
                        setHeader(ExtServiceContext.ROLE_ID, roleIds);
                        setCache(ExtServiceContext.ROLE_ID, userId, roleIds);
                    }
                }
            }

            if (getCache(ExtServiceContext.POST_CODE, userId) != null) {
                log.info("post code is not null, post code is [{}]", getCache(ExtServiceContext.POST_CODE, userId));
                setHeader(ExtServiceContext.POST_CODE, getCache(ExtServiceContext.POST_CODE, userId));
                setHeader(ExtServiceContext.POST_NAME, getCache(ExtServiceContext.POST_NAME, userId));
            } else {
                try {
                    RestResponse<List<PostExtRespDto>> postList = postQueryExtApi.queryPostByUserId(userId);
                    if (!postList.getData().isEmpty()) {
                        String codes = postList.getData().stream().map(PostExtRespDto::getCode).collect(Collectors.joining(","));
                        String names = postList.getData().stream().map(PostExtRespDto::getName).collect(Collectors.joining(","));
                        setHeader(ExtServiceContext.POST_CODE, codes);
                        setHeader(ExtServiceContext.POST_NAME, names);
                        setCache(ExtServiceContext.POST_CODE, userId, codes);
                        setCache(ExtServiceContext.POST_NAME, userId, names);
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            if (getCache(ExtServiceContext.USER_NAME, userId) != null) {
                log.info("user name is not null, user name is [{}]", getCache(ExtServiceContext.USER_NAME, userId));
                setHeader(ExtServiceContext.USER_NAME, getCache(ExtServiceContext.USER_NAME, userId));
            } else {
                RestResponse<List<EmployeeDto>> employeeDtoList = organizationQueryApi.queryEmployeeByUserId(userId, "{}");
                if (!employeeDtoList.getData().isEmpty()) {
                    EmployeeDto employeeDto = employeeDtoList.getData().get(0);
                    setHeader(ExtServiceContext.USER_NAME, employeeDto.getName());
                    setLongCache(ExtServiceContext.USER_NAME, userId, employeeDto.getName());
                }
            }
            if (getCache(ExtServiceContext.USER_CODE, userId) != null) {
                setHeader(ExtServiceContext.USER_CODE, getCache(ExtServiceContext.USER_CODE, userId));
            } else {
                RestResponse<UserDto> userDto = userQueryApi.queryById(userId, "{}");
                if (Objects.nonNull(userDto.getData())) {
                    setHeader(ExtServiceContext.USER_CODE, userDto.getData().getUserName());
                    setLongCache(ExtServiceContext.USER_CODE, userId, userDto.getData().getUserName());
                }
            }
            log.info(JSON.toJSONString(ExtServiceContext.getUserName()));
        } catch (Exception e) {
            log.info("", e);
        } finally {
            userInfo.remove();
        }
        return true;
    }

    private String getCache(String key, Long userId) {
        log.info("获取缓存的内容: [{}]", redisCache.getCache(CACHE_PREFIX + key + userId.toString(), String.class));
        return redisCache.getCache(CACHE_PREFIX + key + userId.toString(), String.class);
    }

    private void setCache(String key, Long userId, String value) {
        log.info("设置缓存的内容, key: [{}], userId:[{}], value: [{}]", key, userId, value);
        redisCache.setCache(CACHE_PREFIX + key + userId.toString(), value, CACHE_TIME_OUT);
    }

    private void setLongCache(String key, Long userId, String value) {
        redisCache.setCache(CACHE_PREFIX + key + userId.toString(), value, CACHE_TIME_OUT * 6);
    }

    private <T> void setHeader(String header, T value) {
        if (value != null) {
            try {
                ExtServiceContext.put(header, URLDecoder.decode(value.toString(), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                log.warn(e.getMessage(), e);
            }
        }

    }
}