spring:
  main:
    # 容许bean定义被覆盖
    allow-bean-definition-overriding: true
  aop:
    # aop代理方式
    proxy-target-class: true
  application:
    name: cathayfuture-opm
  cloud:
    nacos:
      config:
        sharedConfigs: yundt-cube-commons.yaml
        file-extension: yaml
      discovery:
        enabled: true

feign:
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 150000
        readTimeout: 150000












