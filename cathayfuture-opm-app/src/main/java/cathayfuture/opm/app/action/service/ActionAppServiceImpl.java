package cathayfuture.opm.app.action.service;

import cathayfuture.opm.client.action.api.ActionAppService;
import cathayfuture.opm.client.action.dto.request.ActionReqDTO;
import cathayfuture.opm.client.action.dto.response.ActionRespDTO;
import cathayfuture.opm.client.student.dto.request.StudentReqDTO;
import cathayfuture.opm.domain.action.ActionEntity;
import cathayfuture.opm.domain.action.enums.ActionCodeEnum;
import cathayfuture.opm.domain.action.enums.FieldEnum;
import cathayfuture.opm.domain.action.enums.ModuleEnum;
import cathayfuture.opm.domain.action.repository.ActionRepository;
import cathayfuture.opm.domain.order.enums.BusinessUnitEnum;
import cathayfuture.opm.domain.student.enums.GenderEnum;
import cathayfuture.opm.infra.action.repository.mapper.ActionMapper;
import cathayfuture.opm.infra.common.utils.IdUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class ActionAppServiceImpl implements ActionAppService {

    @Autowired
    private ActionRepository actionRepository;

    private ActionMapper actionMapper;

    public ActionAppServiceImpl(ActionMapper actionMapper) {
        this.actionMapper = actionMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addBatch(List<ActionReqDTO> list) {
        if(CollectionUtil.isEmpty(list)){
            return null;
        }

        ActionEntity actionEntity;
        List<ActionEntity> actionEntityList = new ArrayList<>();
        for(int i = 0; i < list.size(); i++){
            ActionReqDTO actionReqDTO = list.get(i);
            actionEntity = new ActionEntity();
            BeanUtils.copyProperties(actionReqDTO, actionEntity);
            actionEntity.setDr(0);
            actionEntityList.add(actionEntity);
        }

        return actionRepository.addBatch(actionEntityList);
    }

    @Override
    public List<ActionReqDTO> collectDiff(StudentReqDTO studentNew, StudentReqDTO studentOld) {
        String module = ModuleEnum.STUDENT.getKey();
        String actionId = UUID.randomUUID().toString();
        String actionCode = ActionCodeEnum.EDIT.getKey();
        Integer businessId = studentNew.getId();

        ActionReqDTO actionReqDTO;
        List<ActionReqDTO> actionReqDTOList = new ArrayList<>();
        String[] fieldNameArr = new String[]{"registerStatus","studentName", "studentNo", "studentClass", "ethnicGroup", "idNumber", "gender", "birthday", "nationality", "contactName", "contactPhoneNumber", "relation", "fatherName", "fatherPhoneNumber", "motherName", "motherPhoneNumber", "admissionDate", "businessUnit", "provinceName", "cityName", "regionName", "homeAddress"};

        for(int i = 0; i < fieldNameArr.length; i++ ) {
            String newVal = getFieldValueByName(studentNew, fieldNameArr[i]) == null ? null : String.valueOf(getFieldValueByName(studentNew, fieldNameArr[i]));
            String oldVal = getFieldValueByName(studentOld, fieldNameArr[i]) == null ? null : String.valueOf(getFieldValueByName(studentOld, fieldNameArr[i]));
            if(!StringUtils.equals(newVal, oldVal)){
                actionReqDTO = new ActionReqDTO();
                actionReqDTO.setModule(module);
                actionReqDTO.setActionId(actionId);
                actionReqDTO.setActionCode(actionCode);
                actionReqDTO.setBusinessId(businessId);
                actionReqDTO.setProperty(fieldNameArr[i]);
                actionReqDTO.setNewVal(newVal);
                actionReqDTO.setOldVal(oldVal);
                actionReqDTOList.add(actionReqDTO);
            }
        }

        return actionReqDTOList;
    }

    public Object getFieldValueByName(Object o, String fieldName) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(o, new Object[] {});
            return value;
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return null;
        }
    }

    @Override
    public List<ActionRespDTO> actionList(String module, Integer businessId) {
        List<ActionEntity> actionEntityList = actionMapper
                .selectList(Wrappers.lambdaQuery(ActionEntity.class)
                        .orderByDesc(ActionEntity::getCreateTime, ActionEntity::getId)
                        .eq(ActionEntity::getModule, module)
                        .eq(ActionEntity::getBusinessId, businessId));

        List<ActionEntity> list = actionEntityList.stream()
                .filter(x -> FieldEnum.getAllPropertyList().contains(x.getProperty()))
                .collect(Collectors.toList());

        return list.stream().map(actionEntity -> {
            ActionRespDTO actionRespDTO = new ActionRespDTO();
            BeanUtils.copyProperties(actionEntity, actionRespDTO);
            if(StringUtils.equals(FieldEnum.GENDER.getKey(), actionRespDTO.getProperty())) {
                actionRespDTO.setNewVal(Optional.ofNullable(actionEntity.getNewVal()).map(Integer::valueOf).map(GenderEnum::getEnumDescription).orElse(""));
                actionRespDTO.setOldVal(Optional.ofNullable(actionEntity.getOldVal()).map(Integer::valueOf).map(GenderEnum::getEnumDescription).orElse(""));
            }
            if(StringUtils.equals(FieldEnum.BUSINESSUNIT.getKey(), actionRespDTO.getProperty())) {
                actionRespDTO.setNewVal(Optional.ofNullable(actionEntity.getNewVal()).map(Integer::valueOf).map(BusinessUnitEnum::getEnumDescription).orElse(""));
                actionRespDTO.setOldVal(Optional.ofNullable(actionEntity.getOldVal()).map(Integer::valueOf).map(BusinessUnitEnum::getEnumDescription).orElse(""));
            }
            return actionRespDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public <T> List<ActionReqDTO> collectDiff(T newData, T oldData, String actionCode, String module){
        List<String> ignoreList= Lists.newArrayList("dr","createTime","updateTime","createPerson","updatePerson");

        boolean add = Objects.equals(ActionCodeEnum.ADD.getKey(), actionCode);
        boolean delete = Objects.equals(ActionCodeEnum.DELETE.getKey(), actionCode);
        if(!( add || delete)){
            if(!Objects.equals(newData.getClass(),oldData.getClass())){
//                throw new BizException("两个参数不属于同一个类型 ：newData："+newData.getClass()+"oldData："+oldData.getClass());
            }
        }
        String actionId = IdUtils.getNextId().toString();
        Integer businessId = (Integer) ReflectUtil.getFieldValue(newData, "id");

        List<ActionReqDTO> actionReqDTOList = new ArrayList<>();
        if(delete){
            ActionReqDTO actionReqDTO = new ActionReqDTO();
            actionReqDTO.setModule(module);
            actionReqDTO.setActionId(actionId);
            actionReqDTO.setActionCode(actionCode);
            actionReqDTO.setBusinessId(businessId);
            actionReqDTO.setProperty("dr");
            actionReqDTO.setNewVal("1");
            actionReqDTO.setOldVal("0");
            actionReqDTOList.add(actionReqDTO);
        }else{
            Field[] fields = ReflectUtil.getFields(newData.getClass());
            for (Field field : fields) {
                if(ignoreList.contains(field.getName())){
                    continue;
                }
                String newVal = Optional.ofNullable(ReflectUtil.getFieldValue(newData, field))
                        .map(String::valueOf)
                        .orElse(null);
                String oldVal = Optional.ofNullable(ReflectUtil.getFieldValue(oldData, field))
                        .map(String::valueOf)
                        .orElse(null);

                if(!Objects.equals(newVal, oldVal)){
                    ActionReqDTO actionReqDTO = new ActionReqDTO();
                    actionReqDTO.setModule(module);
                    actionReqDTO.setActionId(actionId);
                    actionReqDTO.setActionCode(actionCode);
                    actionReqDTO.setBusinessId(businessId);
                    actionReqDTO.setProperty(field.getName());
                    actionReqDTO.setNewVal(newVal);
                    actionReqDTO.setOldVal(oldVal);
                    actionReqDTOList.add(actionReqDTO);
                }
            }
        }
        return actionReqDTOList;

    }
}
