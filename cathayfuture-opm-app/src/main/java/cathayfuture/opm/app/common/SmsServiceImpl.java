package cathayfuture.opm.app.common;

import cathayfuture.opm.client.common.SmsService;
import cathayfuture.opm.client.common.dto.request.CheckSmsVerificationCodeReqDTO;
import cathayfuture.opm.client.common.dto.request.SendSmsReqDTO;
import cathayfuture.opm.client.common.exception.SmsSendException;
import cathayfuture.opm.infra.common.AliSmsUtils;
import cn.hutool.core.util.RandomUtil;
import com.dtyunxi.huieryun.cache.api.IRedisCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 *
 *
 * <AUTHOR>
 * @since 20220829
 */
@Slf4j
@Service
@RefreshScope
public class SmsServiceImpl implements SmsService {

    private static final String SMS_VERIFICATION_CODE_CACHE_GROUP = "SMS_VERIFICATION_CODE_CACHE";
    private static final String SMS_VERIFICATION_CODE_CACHE_TIMEOUT_GROUP = "SMS_VERIFICATION_CODE_TIMEOUT_CACHE";
    private static final Integer SMS_VERIFICATION_CODE_CACHE_TIMEOUT_SECONDS = 5 * 60;
    private static final Integer SMS_VERIFICATION_CODE_TIMEOUT_CACHE_TIMEOUT_SECONDS = 1 * 60;

    @Value("${spring.profiles.active}")
    private String profilesActive;
    @Value("${sms.send.profiles}")
    private List<String> sendProfiles;

    @Resource
    private IRedisCacheService cacheService;

    @Override
    public Boolean sendSms(SendSmsReqDTO sendSmsReqDTO) {
        return sendSmsVerificationCode(sendSmsReqDTO);
    }

    public Boolean sendSmsVerificationCode(SendSmsReqDTO sendSmsReqDTO) {
        String cachedSmsVerificationCode = cacheService.getCache(SMS_VERIFICATION_CODE_CACHE_TIMEOUT_GROUP, sendSmsReqDTO.getPhoneNumber(), String.class);
        if (StringUtils.isNotEmpty(cachedSmsVerificationCode)) {
            throw new SmsSendException("该手机号已发送验证码，请稍后重试");
        }
        int randomInt = RandomUtil.randomInt(999999);
        String smsVerificationCode = String.format("%06d", randomInt);
        if (isSendSms()) {
            AliSmsUtils.sendSmsVerificationCode(sendSmsReqDTO.getPhoneNumber(), smsVerificationCode);
        }
        cacheService.setCache(SMS_VERIFICATION_CODE_CACHE_GROUP, sendSmsReqDTO.getPhoneNumber(), smsVerificationCode, SMS_VERIFICATION_CODE_CACHE_TIMEOUT_SECONDS);
        cacheService.setCache(SMS_VERIFICATION_CODE_CACHE_TIMEOUT_GROUP, sendSmsReqDTO.getPhoneNumber(), smsVerificationCode, SMS_VERIFICATION_CODE_TIMEOUT_CACHE_TIMEOUT_SECONDS);
        return true;
    }

    @Override
    public Boolean checkSmsVerificationCode(CheckSmsVerificationCodeReqDTO checkSmsVerificationCodeReqDTO) {
        if (!isSendSms()) {
            cacheService.delCache(SMS_VERIFICATION_CODE_CACHE_GROUP, checkSmsVerificationCodeReqDTO.getPhoneNumber());
            cacheService.delCache(SMS_VERIFICATION_CODE_CACHE_TIMEOUT_GROUP, checkSmsVerificationCodeReqDTO.getPhoneNumber());
            return true;
        }
        String cachedSmsVerificationCode = cacheService.getCache(SMS_VERIFICATION_CODE_CACHE_GROUP, checkSmsVerificationCodeReqDTO.getPhoneNumber(), String.class);
        if (Objects.equals(cachedSmsVerificationCode, checkSmsVerificationCodeReqDTO.getSmsVerificationCode())) {
            cacheService.delCache(SMS_VERIFICATION_CODE_CACHE_GROUP, checkSmsVerificationCodeReqDTO.getPhoneNumber());
            cacheService.delCache(SMS_VERIFICATION_CODE_CACHE_TIMEOUT_GROUP, checkSmsVerificationCodeReqDTO.getPhoneNumber());
            return true;
        }
        return false;
    }
    
    public boolean isSendSms() {
        if (CollectionUtils.isEmpty(sendProfiles)) {
            return false;
        }
        return sendProfiles.stream().anyMatch(f -> profilesActive.contains(f));
    }
}