package cathayfuture.opm.infra.attendance.repository.mapper;

import cathayfuture.opm.client.attendance.dto.response.AttendanceFirstRespDto;
import cathayfuture.opm.client.order.dto.bo.BuildOrderBo;
import cathayfuture.opm.domain.attendance.AttendanceRecordEntity;
import cathayfuture.opm.infra.common.basequery.ExtBaseMapper;
import net.bytebuddy.asm.Advice;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface AttendanceRecordMapper extends ExtBaseMapper<AttendanceRecordEntity> {

    @Select("select student_id, min(attendance_date) AS firstDay from of_stu_attendance_record osar where dr = 0 and normal_flg=1 group by student_id; ")
    List<AttendanceFirstRespDto> queryFirstDayMap();

}
