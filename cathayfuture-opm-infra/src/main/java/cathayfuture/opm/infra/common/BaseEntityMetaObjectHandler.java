package cathayfuture.opm.infra.common;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.dtyunxi.app.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 描述:
 *
 * <AUTHOR>
 * @date 9/9/22
 */
@Slf4j
@Component
public class BaseEntityMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("start insert fill ....");
        log.info("tenantId is [{}]", ServiceContext.getContext().getRequestTenantIdString());
        this.strictInsertFill(metaObject, "tenantId", String.class, ServiceContext.getContext().getRequestTenantIdString());
        log.info("instanceId is [{}]", ServiceContext.getContext().getRequestInstanceId());
        this.strictInsertFill(metaObject, "instanceId", Long.class, ServiceContext.getContext().getRequestInstanceId());
        log.info("createPerson is [{}]", ExtServiceContext.getUserName());
        this.strictInsertFill(metaObject, "createPerson", String.class, ServiceContext.getContext().getRequestUserCode());
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        log.info("updatePerson is [{}]", ExtServiceContext.getUserName());
        this.strictInsertFill(metaObject, "updatePerson", String.class,  ServiceContext.getContext().getRequestUserCode());
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("updatePerson is [{}]", ExtServiceContext.getUserName());
        this.setFieldValByName("updatePerson", ServiceContext.getContext().getRequestUserCode(), metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }
}
