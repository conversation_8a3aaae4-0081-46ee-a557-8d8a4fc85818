package cathayfuture.opm.infra.common;

import com.dtyunxi.app.ServiceContext;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;

/**
 * 描述:
 *
 * <AUTHOR>
 * @since 8/29/22
 */
@Slf4j
public class ExtServiceContext {

    private static final String BASE_KEY = "x-dtyunxi-context-";
    public static final String ROLE_ID = "yes.req.roleId";
    public static final String USER_CODE = "yes.req.userCode";
    public static final String USER_NAME = "yes.req.userName";
    public static final String POST_CODE = "yes.req.postCode";
    public static final String POST_NAME = "yes.req.postName";

    public static final String USER_ID = "yes.req.userId";

    protected static final String[] CONTEXT_KEYS = new String[]{ROLE_ID, USER_CODE, USER_NAME, POST_CODE, POST_NAME};


    public static String getRoleId() {
        return (String) ServiceContext.getContext().get(ROLE_ID);
    }

    public static String getUserCode() {
        return (String) ServiceContext.getContext().get(USER_CODE);
    }


    public static String getUserName() {
        log.info("当前的用户名称是,[{}]", (String) ServiceContext.getContext().get(USER_NAME));
        return (String) ServiceContext.getContext().get(USER_NAME);
    }


    public static String getPostCode() {
        return (String) ServiceContext.getContext().get(POST_CODE);
    }


    public static String getPostName() {
        return (String) ServiceContext.getContext().get(POST_NAME);
    }


    public static String getHeaderKey(String key) {
        return BASE_KEY + key.toLowerCase();
    }

    public static void put(String key, Object value) {
        log.info("当前的context键值, key:[{}], value:[{}]", key, value.toString());
        ServiceContext.getContext().set(key, value);
    }

    public static void init(Map<String, Object> headers) {
        for (String key : CONTEXT_KEYS) {
            ServiceContext.getContext().remove(key);
        }
        for (String key : ExtServiceContext.CONTEXT_KEYS) {
            String value = (String) headers.get(ExtServiceContext.getHeaderKey(key));
            if (value != null) {
                try {
                    ExtServiceContext.put(key, URLDecoder.decode(value, "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    log.info("init the ExtServiceContext error!, the error message is [{}]", e.getMessage(), e);
                }
            }
        }
    }

}