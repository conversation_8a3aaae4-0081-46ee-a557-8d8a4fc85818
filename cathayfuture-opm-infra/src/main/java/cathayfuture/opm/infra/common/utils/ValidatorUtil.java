package cathayfuture.opm.infra.common.utils;

import org.apache.commons.collections.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class ValidatorUtil {

    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    public static <T> void validate(T obj, Class... groups) {
        Set<ConstraintViolation<T>> set = validator.validate(obj, groups);
        if (CollectionUtils.isNotEmpty(set)) {
            throw new ConstraintViolationException("ConstraintViolationException:", set);
        }
    }

    public static <T> List<String> validateMessages(T obj, Class... groups) {
        List<String> messages = new ArrayList<String>();
        Set<ConstraintViolation<T>> set = validator.validate(obj, groups);
        if(set!=null) {
            for (ConstraintViolation c : set) {
                messages.add(c.getMessage());
            }
        }
        return messages;
    }}