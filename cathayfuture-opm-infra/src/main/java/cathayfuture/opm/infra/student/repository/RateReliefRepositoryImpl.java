package cathayfuture.opm.infra.student.repository;

import cathayfuture.opm.domain.student.RateReliefEntity;
import cathayfuture.opm.domain.student.repository.RateReliefRepository;
import cathayfuture.opm.infra.common.basequery.wrapper.ExtQueryWrapper;
import cathayfuture.opm.infra.common.lambda.LambdaUtils;
import cathayfuture.opm.infra.student.repository.mapper.RateReliefMapper;
import com.dtyunxi.exceptions.BizException;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 费用管理
 * @date 2023/3/31 10:02
 */
@Repository
public class RateReliefRepositoryImpl implements RateReliefRepository {

    private RateReliefMapper mapper;

    public RateReliefRepositoryImpl(RateReliefMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public void add(RateReliefEntity entity){
        if(Objects.nonNull(entity)){
            mapper.insert(entity);
        }
    }

    @Override
    public void delete(Integer id){
        if(Objects.isNull(id)){
            throw new BizException("RateReliefRepository.delete 入参 id 为null");
        }
        mapper.deleteById(id);
    }

    @Override
    public RateReliefEntity findById(Integer id){
        if(Objects.isNull(id)){
            throw new BizException("RateReliefRepository.findById 入参 id 为null");
        }
        return mapper.selectById(id);
    }

    @Override
    public List<RateReliefEntity> findListByIds(List<Integer> list){
        List<RateReliefEntity> entities = mapper.selectList(ExtQueryWrapper.newInstance(RateReliefEntity.class)
                .in(LambdaUtils.column(RateReliefEntity::getStudentId),list)
        );
        return entities;
    }
}
